#===============================================================================
#============================== PROJECT METADATA ===============================
#===============================================================================
[project]
name = "Funshade"
version = "1.0.2"
description = "Function Secret Sharing for Multiparty Computation"
readme = "README.md"
requires-python = ">=3.8"
license = {text = "GNU GPLv3"}
keywords = ["function-secret-sharing" , "FSS", "Multiparty Computation", "MPC", "cython", "cryptography"]
authors = [
  {name = "<PERSON>do", email = "<EMAIL>"},
]
classifiers = [
    "Programming Language :: C",
    "Programming Language :: Cython",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Development Status :: 3 - Alpha",
    "License :: OSI Approved :: GNU General Public License v3 (GPLv3)",
    "Operating System :: Unix",
    "Topic :: Security",
    "Topic :: Security :: Cryptography",
]

dependencies = [
  "numpy>=1.21"
]

# Minimum requirements for the build system to execute.
[build-system]
requires = [
  "setuptools<=60.9",
  "wheel",
  "cython==3.0.0b1",
  "numpy>=1.21",
  "toml>=0.10"
]

#===============================================================================
#========================= COMPILATION CONFIGURATION ===========================
#===============================================================================
#----------------------------- CYTHON EXTENSIONS -------------------------------
[extensions.config]   # Common compilation config for all extensions
include_dirs = ['funshade/c']
define_macros = [
  ["NPY_NO_DEPRECATED_API", "NPY_1_7_API_VERSION"],
  ["__PYX_ENUM_CLASS_DECL", "enum"], # Support enums in cython
]
extra_compile_args = [
  {Windows = ["/O2",]},
  {Darwin = ["-O3","-msse", "-msse2", "-maes", "-march=native"]},
  {Linux = ["-O3","-msse", "-msse2", "-maes", "-march=native"]},
]
extra_link_args = [
  {Windows = []},
  {Darwin = []},
  {Linux = []},
]
# libraries = ['sodium']  # libraries to link with, cpplibraries above are added by default

# List of extensions to compile. Custom compilation config can be defined for each
[extensions.funshade]
fullname='funshade'    
sources=['funshade/py/funshade.pyx', 'funshade/c/fss.c', 'funshade/c/aes.c']
