# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/build/CMakeFiles /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named test_fss

# Build rule for target.
test_fss: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_fss
.PHONY : test_fss

# fast build rule for target.
test_fss/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fss.dir/build.make CMakeFiles/test_fss.dir/build
.PHONY : test_fss/fast

funshade/c/aes.o: funshade/c/aes.c.o
.PHONY : funshade/c/aes.o

# target to build an object file
funshade/c/aes.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fss.dir/build.make CMakeFiles/test_fss.dir/funshade/c/aes.c.o
.PHONY : funshade/c/aes.c.o

funshade/c/aes.i: funshade/c/aes.c.i
.PHONY : funshade/c/aes.i

# target to preprocess a source file
funshade/c/aes.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fss.dir/build.make CMakeFiles/test_fss.dir/funshade/c/aes.c.i
.PHONY : funshade/c/aes.c.i

funshade/c/aes.s: funshade/c/aes.c.s
.PHONY : funshade/c/aes.s

# target to generate assembly for a file
funshade/c/aes.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fss.dir/build.make CMakeFiles/test_fss.dir/funshade/c/aes.c.s
.PHONY : funshade/c/aes.c.s

funshade/c/fss.o: funshade/c/fss.c.o
.PHONY : funshade/c/fss.o

# target to build an object file
funshade/c/fss.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fss.dir/build.make CMakeFiles/test_fss.dir/funshade/c/fss.c.o
.PHONY : funshade/c/fss.c.o

funshade/c/fss.i: funshade/c/fss.c.i
.PHONY : funshade/c/fss.i

# target to preprocess a source file
funshade/c/fss.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fss.dir/build.make CMakeFiles/test_fss.dir/funshade/c/fss.c.i
.PHONY : funshade/c/fss.c.i

funshade/c/fss.s: funshade/c/fss.c.s
.PHONY : funshade/c/fss.s

# target to generate assembly for a file
funshade/c/fss.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fss.dir/build.make CMakeFiles/test_fss.dir/funshade/c/fss.c.s
.PHONY : funshade/c/fss.c.s

funshade/c/test_fss.o: funshade/c/test_fss.c.o
.PHONY : funshade/c/test_fss.o

# target to build an object file
funshade/c/test_fss.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fss.dir/build.make CMakeFiles/test_fss.dir/funshade/c/test_fss.c.o
.PHONY : funshade/c/test_fss.c.o

funshade/c/test_fss.i: funshade/c/test_fss.c.i
.PHONY : funshade/c/test_fss.i

# target to preprocess a source file
funshade/c/test_fss.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fss.dir/build.make CMakeFiles/test_fss.dir/funshade/c/test_fss.c.i
.PHONY : funshade/c/test_fss.c.i

funshade/c/test_fss.s: funshade/c/test_fss.c.s
.PHONY : funshade/c/test_fss.s

# target to generate assembly for a file
funshade/c/test_fss.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_fss.dir/build.make CMakeFiles/test_fss.dir/funshade/c/test_fss.c.s
.PHONY : funshade/c/test_fss.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... test_fss"
	@echo "... funshade/c/aes.o"
	@echo "... funshade/c/aes.i"
	@echo "... funshade/c/aes.s"
	@echo "... funshade/c/fss.o"
	@echo "... funshade/c/fss.i"
	@echo "... funshade/c/fss.s"
	@echo "... funshade/c/test_fss.o"
	@echo "... funshade/c/test_fss.i"
	@echo "... funshade/c/test_fss.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

