# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/build

# Include any dependencies generated for this target.
include CMakeFiles/test_fss.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/test_fss.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/test_fss.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test_fss.dir/flags.make

CMakeFiles/test_fss.dir/funshade/c/test_fss.c.o: CMakeFiles/test_fss.dir/flags.make
CMakeFiles/test_fss.dir/funshade/c/test_fss.c.o: /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/funshade/c/test_fss.c
CMakeFiles/test_fss.dir/funshade/c/test_fss.c.o: CMakeFiles/test_fss.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/test_fss.dir/funshade/c/test_fss.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/test_fss.dir/funshade/c/test_fss.c.o -MF CMakeFiles/test_fss.dir/funshade/c/test_fss.c.o.d -o CMakeFiles/test_fss.dir/funshade/c/test_fss.c.o -c /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/funshade/c/test_fss.c

CMakeFiles/test_fss.dir/funshade/c/test_fss.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/test_fss.dir/funshade/c/test_fss.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/funshade/c/test_fss.c > CMakeFiles/test_fss.dir/funshade/c/test_fss.c.i

CMakeFiles/test_fss.dir/funshade/c/test_fss.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/test_fss.dir/funshade/c/test_fss.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/funshade/c/test_fss.c -o CMakeFiles/test_fss.dir/funshade/c/test_fss.c.s

CMakeFiles/test_fss.dir/funshade/c/fss.c.o: CMakeFiles/test_fss.dir/flags.make
CMakeFiles/test_fss.dir/funshade/c/fss.c.o: /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/funshade/c/fss.c
CMakeFiles/test_fss.dir/funshade/c/fss.c.o: CMakeFiles/test_fss.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/test_fss.dir/funshade/c/fss.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/test_fss.dir/funshade/c/fss.c.o -MF CMakeFiles/test_fss.dir/funshade/c/fss.c.o.d -o CMakeFiles/test_fss.dir/funshade/c/fss.c.o -c /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/funshade/c/fss.c

CMakeFiles/test_fss.dir/funshade/c/fss.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/test_fss.dir/funshade/c/fss.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/funshade/c/fss.c > CMakeFiles/test_fss.dir/funshade/c/fss.c.i

CMakeFiles/test_fss.dir/funshade/c/fss.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/test_fss.dir/funshade/c/fss.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/funshade/c/fss.c -o CMakeFiles/test_fss.dir/funshade/c/fss.c.s

CMakeFiles/test_fss.dir/funshade/c/aes.c.o: CMakeFiles/test_fss.dir/flags.make
CMakeFiles/test_fss.dir/funshade/c/aes.c.o: /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/funshade/c/aes.c
CMakeFiles/test_fss.dir/funshade/c/aes.c.o: CMakeFiles/test_fss.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/test_fss.dir/funshade/c/aes.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/test_fss.dir/funshade/c/aes.c.o -MF CMakeFiles/test_fss.dir/funshade/c/aes.c.o.d -o CMakeFiles/test_fss.dir/funshade/c/aes.c.o -c /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/funshade/c/aes.c

CMakeFiles/test_fss.dir/funshade/c/aes.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/test_fss.dir/funshade/c/aes.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/funshade/c/aes.c > CMakeFiles/test_fss.dir/funshade/c/aes.c.i

CMakeFiles/test_fss.dir/funshade/c/aes.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/test_fss.dir/funshade/c/aes.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/funshade/c/aes.c -o CMakeFiles/test_fss.dir/funshade/c/aes.c.s

# Object files for target test_fss
test_fss_OBJECTS = \
"CMakeFiles/test_fss.dir/funshade/c/test_fss.c.o" \
"CMakeFiles/test_fss.dir/funshade/c/fss.c.o" \
"CMakeFiles/test_fss.dir/funshade/c/aes.c.o"

# External object files for target test_fss
test_fss_EXTERNAL_OBJECTS =

test_fss: CMakeFiles/test_fss.dir/funshade/c/test_fss.c.o
test_fss: CMakeFiles/test_fss.dir/funshade/c/fss.c.o
test_fss: CMakeFiles/test_fss.dir/funshade/c/aes.c.o
test_fss: CMakeFiles/test_fss.dir/build.make
test_fss: CMakeFiles/test_fss.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking C executable test_fss"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/test_fss.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/test_fss.dir/build: test_fss
.PHONY : CMakeFiles/test_fss.dir/build

CMakeFiles/test_fss.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/test_fss.dir/cmake_clean.cmake
.PHONY : CMakeFiles/test_fss.dir/clean

CMakeFiles/test_fss.dir/depend:
	cd /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/build /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/build /home/<USER>/Desktop/locate_bird_in_air/funshade-smpc/build/CMakeFiles/test_fss.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/test_fss.dir/depend

